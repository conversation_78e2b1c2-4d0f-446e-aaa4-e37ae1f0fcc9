package postgres

import (
	"context"
)

// ExampleUsageOfCommonType demonstrates how to use the unified UserWithProfileAndContactRow type
// for all three list queries: ListAllUsers, ListNotDeletedUsers, and ListDeletedUsers
func ExampleUsageOfCommonType(ctx context.Context, q *Queries) ([]UserWithProfileAndContactRow, error) {
	var allUsers []UserWithProfileAndContactRow
	
	// Example 1: Get all users (both deleted and non-deleted)
	allUsersResult, err := q.ListAllUsers(ctx, ListAllUsersParams{
		Limit:  10,
		Offset: 0,
	})
	if err != nil {
		return nil, err
	}
	
	// Since UserWithProfileAndContactRow is an alias to ListAllUsersRow,
	// we can directly append without conversion
	for _, user := range allUsersResult {
		allUsers = append(allUsers, UserWithProfileAndContactRow(user))
	}
	
	// Example 2: Get only non-deleted users
	notDeletedResult, err := q.ListNotDeletedUsers(ctx, ListNotDeletedUsersParams{
		Limit:  10,
		Offset: 0,
	})
	if err != nil {
		return nil, err
	}
	
	// Convert using helper function
	notDeletedConverted := FromListNotDeletedUsersRows(notDeletedResult)
	allUsers = append(allUsers, notDeletedConverted...)
	
	// Example 3: Get only deleted users
	deletedResult, err := q.ListDeletedUsers(ctx, ListDeletedUsersParams{
		Limit:  10,
		Offset: 0,
	})
	if err != nil {
		return nil, err
	}
	
	// Convert using helper function
	deletedConverted := FromListDeletedUsersRows(deletedResult)
	allUsers = append(allUsers, deletedConverted...)
	
	return allUsers, nil
}

// ProcessUsersUniformly shows how you can process all user types uniformly
func ProcessUsersUniformly(users []UserWithProfileAndContactRow) map[string]interface{} {
	stats := map[string]interface{}{
		"total_users":     len(users),
		"approved_users":  0,
		"banned_users":    0,
		"users_with_email": 0,
		"users_with_profile": 0,
	}
	
	for _, user := range users {
		if user.UserApproved {
			stats["approved_users"] = stats["approved_users"].(int) + 1
		}
		if user.UserBanned {
			stats["banned_users"] = stats["banned_users"].(int) + 1
		}
		if user.ContactEmail.Valid && user.ContactEmail.String != "" {
			stats["users_with_email"] = stats["users_with_email"].(int) + 1
		}
		if user.ProfileID.Valid {
			stats["users_with_profile"] = stats["users_with_profile"].(int) + 1
		}
	}
	
	return stats
}

// FilterUsersByRole demonstrates filtering users by role using the common type
func FilterUsersByRole(users []UserWithProfileAndContactRow, targetRole string) []UserWithProfileAndContactRow {
	var filtered []UserWithProfileAndContactRow
	
	for _, user := range users {
		for _, role := range user.UserRoles {
			if role == targetRole {
				filtered = append(filtered, user)
				break
			}
		}
	}
	
	return filtered
}
