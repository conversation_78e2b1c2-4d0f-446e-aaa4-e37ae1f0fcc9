package postgres

// Common type aliases for user list queries
// All three list queries (ListAllUsers, ListNotDeletedUsers, ListDeletedUsers) 
// return the same structure, so we can use type aliases to have a single type for all of them.
//
// Usage example:
//   var users []UserWithProfileAndContactRow
//   users = append(users, UserWithProfileAndContactRow(listAllResult)...)
//   users = append(users, UserWithProfileAndContactRow(listNotDeletedResult)...)
//   users = append(users, UserWithProfileAndContactRow(listDeletedResult)...)

// UserWithProfileAndContactRow is the common type for all user list queries
// It's an alias to ListAllUsersRow since all three queries have identical SELECT clauses
type UserWithProfileAndContactRow = ListAllUsersRow

// Additional type aliases for convenience and backward compatibility
type ListUsersRow = ListAllUsersRow

// Helper functions to convert between types (though they're the same underlying type)

// ToUserWithProfileAndContactRow converts any of the list query results to the common type
func ToUserWithProfileAndContactRow(row ListAllUsersRow) UserWithProfileAndContactRow {
	return UserWithProfileAndContactRow(row)
}

// ToUserWithProfileAndContactRows converts slices of any list query results to the common type
func ToUserWithProfileAndContactRows(rows []ListAllUsersRow) []UserWithProfileAndContactRow {
	result := make([]UserWithProfileAndContactRow, len(rows))
	for i, row := range rows {
		result[i] = UserWithProfileAndContactRow(row)
	}
	return result
}

// FromListNotDeletedUsersRows converts ListNotDeletedUsersRow slice to common type
func FromListNotDeletedUsersRows(rows []ListNotDeletedUsersRow) []UserWithProfileAndContactRow {
	result := make([]UserWithProfileAndContactRow, len(rows))
	for i, row := range rows {
		// Since all types have identical structure, we can convert via ListAllUsersRow
		result[i] = UserWithProfileAndContactRow(ListAllUsersRow(row))
	}
	return result
}

// FromListDeletedUsersRows converts ListDeletedUsersRow slice to common type
func FromListDeletedUsersRows(rows []ListDeletedUsersRow) []UserWithProfileAndContactRow {
	result := make([]UserWithProfileAndContactRow, len(rows))
	for i, row := range rows {
		// Since all types have identical structure, we can convert via ListAllUsersRow
		result[i] = UserWithProfileAndContactRow(ListAllUsersRow(row))
	}
	return result
}
