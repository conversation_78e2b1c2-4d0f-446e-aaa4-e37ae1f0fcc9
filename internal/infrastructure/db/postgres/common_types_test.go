package postgres

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
)

func TestUserWithProfileAndContactRowCompatibility(t *testing.T) {
	// Create sample data for testing
	sampleTime := time.Now()
	sampleUUID := uuid.New()

	// Test that all three row types have the same structure
	listAllRow := ListAllUsersRow{
		UserID:                1,
		UserIdentifier:        sampleUUID.String(),
		UserApproved:          true,
		UserBanned:            false,
		UserMetaData:          []byte(`{"test": "data"}`),
		UserRoles:             []string{"admin", "user"},
		UserExpiresAt:         pgtype.Timestamptz{Time: sampleTime, Valid: true},
		UserCreatedAt:         sampleTime,
		UserUpdatedAt:         pgtype.Timestamptz{Time: sampleTime, Valid: true},
		ContactID:             pgtype.Int8{Int64: 1, Valid: true},
		ContactIdentifier:     pgtype.Text{String: "contact-123", Valid: true},
		ContactType:           NullContactType{ContactType: ContactTypeEMAIL, Valid: true},
		ContactMobile:         pgtype.Text{String: "+1234567890", Valid: true},
		ContactEmail:          pgtype.Text{String: "<EMAIL>", Valid: true},
		ContactMobileVerified: pgtype.Bool{Bool: true, Valid: true},
		ContactEmailVerified:  pgtype.Bool{Bool: true, Valid: true},
		ContactMetaData:       []byte(`{"contact": "meta"}`),
		ContactCreatedAt:      pgtype.Timestamptz{Time: sampleTime, Valid: true},
		ProfileID:             pgtype.Int8{Int64: 1, Valid: true},
		ProfileIdentifier:     pgtype.Text{String: "profile-123", Valid: true},
		ProfileType:           NullProfileType{ProfileType: ProfileTypeNATURAL, Valid: true},
		ProfileFirstName:      pgtype.Text{String: "John", Valid: true},
		ProfileLastName:       pgtype.Text{String: "Doe", Valid: true},
		ProfileNationalID:     pgtype.Text{String: "123456789", Valid: true},
		ProfileStatus:         NullProfileStatus{ProfileStatus: ProfileStatusAPPROVED, Valid: true},
		ProfileMetaData:       []byte(`{"profile": "meta"}`),
		ProfileCreatedAt:      pgtype.Timestamptz{Time: sampleTime, Valid: true},
	}

	// Test type conversion to common type
	commonRow := UserWithProfileAndContactRow(listAllRow)
	if commonRow.UserID != listAllRow.UserID {
		t.Errorf("Expected UserID %d, got %d", listAllRow.UserID, commonRow.UserID)
	}

	// Test that we can use the common type in a slice
	var users []UserWithProfileAndContactRow
	users = append(users, commonRow)

	if len(users) != 1 {
		t.Errorf("Expected 1 user, got %d", len(users))
	}

	// Test helper functions
	rows := []ListAllUsersRow{listAllRow}
	convertedRows := ToUserWithProfileAndContactRows(rows)

	if len(convertedRows) != 1 {
		t.Errorf("Expected 1 converted row, got %d", len(convertedRows))
	}

	if convertedRows[0].UserID != listAllRow.UserID {
		t.Errorf("Expected UserID %d, got %d", listAllRow.UserID, convertedRows[0].UserID)
	}
}

func TestTypeAliasesAreIdentical(t *testing.T) {
	// This test verifies that the type aliases are truly identical
	// and can be used interchangeably

	var listAllRow ListAllUsersRow
	var commonRow UserWithProfileAndContactRow
	var listRow ListUsersRow

	// These assignments should work without explicit conversion
	// because they are type aliases, not new types
	commonRow = listAllRow
	listRow = listAllRow
	listAllRow = commonRow

	// Verify they have the same zero values
	if listAllRow.UserID != commonRow.UserID || commonRow.UserID != listRow.UserID {
		t.Error("Type aliases should have identical zero values")
	}
}
