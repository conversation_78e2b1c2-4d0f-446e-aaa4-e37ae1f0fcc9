-- name: ListAllUsers :many
SELECT
    u.id as user_id,
    u.identifier as user_identifier,
    u.approved as user_approved,
    u.banned as user_banned,
    u.meta_data as user_meta_data,
    u.roles as user_roles,
    u.expires_at as user_expires_at,
    u.created_at as user_created_at,
    u.updated_at as user_updated_at,
    c.id as contact_id,
    c.identifier as contact_identifier,
    c.contact_type as contact_type,
    c.mobile as contact_mobile,
    c.email as contact_email,
    c.is_mobile_verified as contact_mobile_verified,
    c.is_email_verified as contact_email_verified,
    c.meta_data as contact_meta_data,
    c.created_at as contact_created_at,
    p.id as profile_id,
    p.identifier as profile_identifier,
    p.profile_type as profile_type,
    p.first_name as profile_first_name,
    p.last_name as profile_last_name,
    p.national_id as profile_national_id,
    p.status as profile_status,
    p.meta_data as profile_meta_data,
    p.created_at as profile_created_at
FROM users u
LEFT JOIN contacts c ON c.user_id = u.id AND c.deleted_at IS NULL
LEFT JOIN profiles p ON p.user_id = u.id AND p.deleted_at IS NULL
ORDER BY u.id DESC
LIMIT $1 OFFSET $2;

-- name: ListNotDeletedUsers :many
SELECT
    u.id as user_id,
    u.identifier as user_identifier,
    u.approved as user_approved,
    u.banned as user_banned,
    u.meta_data as user_meta_data,
    u.roles as user_roles,
    u.expires_at as user_expires_at,
    u.created_at as user_created_at,
    u.updated_at as user_updated_at,
    c.id as contact_id,
    c.identifier as contact_identifier,
    c.contact_type as contact_type,
    c.mobile as contact_mobile,
    c.email as contact_email,
    c.is_mobile_verified as contact_mobile_verified,
    c.is_email_verified as contact_email_verified,
    c.meta_data as contact_meta_data,
    c.created_at as contact_created_at,
    p.id as profile_id,
    p.identifier as profile_identifier,
    p.profile_type as profile_type,
    p.first_name as profile_first_name,
    p.last_name as profile_last_name,
    p.national_id as profile_national_id,
    p.status as profile_status,
    p.meta_data as profile_meta_data,
    p.created_at as profile_created_at
FROM users u
LEFT JOIN contacts c ON c.user_id = u.id AND c.deleted_at IS NULL
LEFT JOIN profiles p ON p.user_id = u.id AND p.deleted_at IS NULL
WHERE u.deleted_at IS NULL
ORDER BY u.id DESC
LIMIT $1 OFFSET $2;

-- name: ListDeletedUsers :many
SELECT
    u.id as user_id,
    u.identifier as user_identifier,
    u.approved as user_approved,
    u.banned as user_banned,
    u.meta_data as user_meta_data,
    u.roles as user_roles,
    u.expires_at as user_expires_at,
    u.created_at as user_created_at,
    u.updated_at as user_updated_at,
    c.id as contact_id,
    c.identifier as contact_identifier,
    c.contact_type as contact_type,
    c.mobile as contact_mobile,
    c.email as contact_email,
    c.is_mobile_verified as contact_mobile_verified,
    c.is_email_verified as contact_email_verified,
    c.meta_data as contact_meta_data,
    c.created_at as contact_created_at,
    p.id as profile_id,
    p.identifier as profile_identifier,
    p.profile_type as profile_type,
    p.first_name as profile_first_name,
    p.last_name as profile_last_name,
    p.national_id as profile_national_id,
    p.status as profile_status,
    p.meta_data as profile_meta_data,
    p.created_at as profile_created_at
FROM users u
LEFT JOIN contacts c ON c.user_id = u.id AND c.deleted_at IS NULL
LEFT JOIN profiles p ON p.user_id = u.id AND p.deleted_at IS NULL
WHERE u.deleted_at IS NOT NULL
ORDER BY u.id DESC
LIMIT $1 OFFSET $2;

-- name: GetUserById :one
SELECT *
FROM users
WHERE id = $1
    AND deleted_at IS NULL
LIMIT 1;

-- name: GetActiveUserById :one
SELECT *
FROM users
WHERE id = $1
    AND banned = false
    AND expires_at > CURRENT_TIMESTAMP
    AND deleted_at IS NULL
LIMIT 1;

-- name: GetActiveUserByIdentifier :one
SELECT *
FROM users
WHERE identifier = $1
    AND banned = false
    AND expires_at > CURRENT_TIMESTAMP
    AND deleted_at IS NULL
LIMIT 1;

-- name: GetUserByIdentifier :one
SELECT *
FROM users
WHERE identifier = $1
    AND deleted_at IS NULL
LIMIT 1;

-- name: GetSafeUserById :one
SELECT id,
    identifier,
    approved,
    banned,
    roles
FROM users
WHERE id = $1
    AND deleted_at IS NULL
LIMIT 1;

-- name: CreateUser :one
INSERT INTO users (
        identifier,
        approved,
        banned,
        meta_data,
        roles,
        expires_at,
        created_at,
        updated_at
    )
VALUES (
        $1,
        $2,
        $3,
        $4,
        $5,
        $6,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    )
RETURNING *;

-- name: UpdateUser :one
UPDATE users
SET (
        approved,
        banned,
        meta_data,
        roles,
        expires_at,
        updated_at
    ) = (
        $1,
        $2,
        $3,
        $4,
        $5,
        CURRENT_TIMESTAMP
    )
WHERE id = $6
RETURNING *;